name: Unit Tests

on:
  pull_request:
    branches:
      - master
      - develop
  merge_group:

jobs:
  unit-tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - uses: actions/cache@v3
        id: pnpm-and-build-cache
        with:
          path: |
            build
            node_modules
          key: ${{ runner.os }}-node_modules-build-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-node_modules-build-

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Lint
        run: pnpm lint

      - name: Run unit tests
        run: pnpm test:unit
