name: Docker

on:
  push:
    branches:
      # Publish `master` as Docker `latest` image.
      - master
      # Publish `develop` as Docker `nightly` image.
      - develop

  # Run tests for all PRs to master and develop.
  pull_request:
    branches:
      - master
      - develop

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: docker/setup-qemu-action@v3
      - uses: docker/setup-buildx-action@v3
      - name: Login to GitHub Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Get release version
        run: |
          RELEASE_VERSION=$([ "${{ github.ref_name }}" = "master" ] && echo "latest" || echo "nightly")
          echo "RELEASE_VERSION=${RELEASE_VERSION}" >> "${GITHUB_ENV}"
      - uses: docker/metadata-action@v5
        id: meta
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=raw,value=${{ env.RELEASE_VERSION }}
      - uses: docker/build-push-action@v5
        with:
          context: .
          target: mermaid
          push: ${{ github.event_name == 'push' }}
          pull: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
