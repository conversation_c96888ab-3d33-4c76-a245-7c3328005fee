name: Update Browserslist database

on:
  schedule:
    - cron: '0 2 1,15 * *'

permissions:
  contents: write
  pull-requests: write

jobs:
  update-browserslist-database:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Configure git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Action"
      - name: Update Browserslist database and create PR if applies
        uses: c2corg/browserslist-update-action@v2
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: browserslist-update
          base_branch: develop
          commit_message: 'build: update Browserslist db'
          title: 'build: update Browserslist db'
          body: Auto-generated by [browserslist-update-action](https://github.com/c2corg/browserslist-update-action/)
          labels: 'chores, github action'
